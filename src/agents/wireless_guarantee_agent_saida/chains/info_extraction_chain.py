from typing import Any, Optional

from an_copilot.framework.chain.copilot_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LLmGeneration
from langchain_core.callbacks import CallbackManagerForChainRun


class InfoExtractionChain(CopilotChain):
    def get_name(self) -> str:
        return self.__class__.__name__

    def description(self) -> str:
        return self.__class__.__doc__

    @property
    def input_keys(self) -> list[str]:
        return ["input"]

    @property
    def output_keys(self) -> list[str]:
        return [self.output_key]

    def _call(
        self,
        inputs: dict[str, Any],
        run_manager: Optional[CallbackManagerForChainRun] = None,
    ) -> dict[str, Any]:
        from src.config import settings
        prompt = settings.get_prompts_factory().get_prompt_template(
            prompt_template_name="INFO_EXTRACTION_PROMPT",
            prompt_id=settings.config.llm.get_prompt_id(),
        )
        prompt_value = prompt.format_prompt(**inputs)
        response = self.llm_generate(
            messages=[prompt_value.to_messages()], run_manager=run_manager
        )
        return self.response_wrapper(LLmGeneration(content=response))
