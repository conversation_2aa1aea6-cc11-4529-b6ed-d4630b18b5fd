"""
无线保障智能体目标检查处理器

本模块实现了WGGoalCheckerProcessor，负责根据当前状态和可选目标列表选择最适合的目标。
实现6种意图类型到对应Goal的映射逻辑，确保与原IntentRecognitionChain的意图分类保持一致。

主要功能：
1. 根据当前状态和意图类型选择合适的目标
2. 实现6种意图类型到Goal的精确映射
3. 处理目标优先级和适用性检查
4. 处理未知意图的默认目标
5. 提供完整的目标选择日志记录

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from typing import List, Optional, ClassVar, Dict, Type

from an_copilot.framework.logging import an_logger
from an_copilot.framework.saida.core.interfaces import IGoalCheckerProcessor

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGContext,
    WGState,
    WGStateEnum,
    WGGoalType,
    WGGoalActiveWorkflow,
    WGGoalApprovalProcess,
    WGGoalExecuteProcess,
    WGGoalEvaluateProcess,
    WGGoalClearWorkflow,
    WGGoalRollbackProcess,
    WGGoalUnknown,
)


def _select_highest_priority_goal(goals: List[WGGoalType]) -> WGGoalType:
    """从目标列表中选择优先级最高的目标"""
    # 按优先级排序，优先级数值越高越优先
    sorted_goals = sorted(goals, key=lambda g: getattr(g, 'priority', 50), reverse=True)
    selected_goal = sorted_goals[0]

    an_logger.info(f"从 {len(goals)} 个候选目标中选择了优先级最高的目标: {selected_goal.name}")
    return selected_goal


class WGGoalCheckerProcessor(IGoalCheckerProcessor[WGState, WGGoalType, WGContext]):
    """
    无线保障智能体目标检查处理器
    
    根据当前状态和可选目标列表选择最适合的目标，实现6种意图类型到对应Goal的映射逻辑。
    确保与原IntentRecognitionChain的意图分类保持一致，支持目标优先级和适用性检查。
    """
    
    # 意图类型常量（与原系统保持一致）
    INTENT_ACTIVE_WORKFLOW: ClassVar[str] = "1"
    INTENT_APPROVAL_PROCESS: ClassVar[str] = "2"
    INTENT_EXECUTE_PROCESS: ClassVar[str] = "3"
    INTENT_EVALUATE_PROCESS: ClassVar[str] = "4"
    INTENT_CLEAR_WORKFLOW: ClassVar[str] = "5"
    INTENT_ROLLBACK_PROCESS: ClassVar[str] = "6"
    
    # 意图类型到Goal类型的映射关系
    INTENT_GOAL_MAPPING: ClassVar[Dict[str, Type[WGGoalType]]] = {
        INTENT_ACTIVE_WORKFLOW: WGGoalActiveWorkflow,
        INTENT_APPROVAL_PROCESS: WGGoalApprovalProcess,
        INTENT_EXECUTE_PROCESS: WGGoalExecuteProcess,
        INTENT_EVALUATE_PROCESS: WGGoalEvaluateProcess,
        INTENT_CLEAR_WORKFLOW: WGGoalClearWorkflow,
        INTENT_ROLLBACK_PROCESS: WGGoalRollbackProcess,
    }
    
    # 状态到意图类型的映射关系（用于状态验证）
    STATE_INTENT_MAPPING: ClassVar[Dict[WGStateEnum, List[str]]] = {
        WGStateEnum.RECOGNIZING: [INTENT_ACTIVE_WORKFLOW],
        WGStateEnum.GENERATING: [],  # 生成状态通常不直接接收外部意图
        WGStateEnum.APPROVING: [INTENT_APPROVAL_PROCESS],
        WGStateEnum.EXECUTING: [INTENT_EXECUTE_PROCESS],
        WGStateEnum.EVALUATING: [INTENT_EVALUATE_PROCESS],
        WGStateEnum.EVALUATED: [],  # 已评估状态通常不直接接收外部意图
        WGStateEnum.RESETTING: [INTENT_ROLLBACK_PROCESS],
        WGStateEnum.INTERRUPTED: [],  # 中断状态通常不直接接收外部意图
        WGStateEnum.ARCHIVED: [],  # 归档状态通常不直接接收外部意图
    }
    
    # 特殊处理：清除工作流可以从任意状态触发
    CLEAR_WORKFLOW_ALLOWED_STATES: ClassVar[List[WGStateEnum]] = [
        WGStateEnum.RECOGNIZING,
        WGStateEnum.GENERATING,
        WGStateEnum.APPROVING,
        WGStateEnum.EXECUTING,
        WGStateEnum.EVALUATING,
        WGStateEnum.EVALUATED,
        WGStateEnum.RESETTING,
        WGStateEnum.INTERRUPTED,
    ]
    
    # 目标优先级配置
    GOAL_PRIORITIES: ClassVar[Dict[Type[WGGoalType], int]] = {
        WGGoalActiveWorkflow: 100,      # 新活动工作流优先级最高
        WGGoalClearWorkflow: 90,        # 清除工作流优先级次高
        WGGoalApprovalProcess: 80,      # 审批流程
        WGGoalExecuteProcess: 70,       # 执行流程
        WGGoalEvaluateProcess: 60,      # 评估流程
        WGGoalRollbackProcess: 50,      # 回退流程
        WGGoalUnknown: 10,              # 未知目标优先级最低
    }
    
    def process(
        self,
        state: WGState,
        goals: List[WGGoalType],
        context: WGContext,
    ) -> Optional[WGGoalType]:
        """
        根据当前状态和可选目标列表选择最适合的目标
        
        Args:
            state: 当前状态对象
            goals: 可选目标列表
            context: 上下文对象
            
        Returns:
            WGGoalType: 选择的目标对象，如果无法选择则返回None
        """
        try:
            # 从上下文中获取意图类型
            intention_type = context.intention_type
            if not intention_type:
                an_logger.warning("上下文中缺少意图类型信息")
                return self._create_unknown_goal("缺少意图类型信息")
            
            # 验证意图类型与当前状态的兼容性
            if not self._validate_intention_state_compatibility(intention_type, state.state):
                return self._create_unknown_goal(
                    f"意图类型 {intention_type} 不兼容当前状态 {state.state.value}"
                )
            
            # 根据意图类型选择目标
            selected_goal = self._select_goal_by_intention(intention_type, goals, context)
            
            if selected_goal:
                self._log_goal_selection(intention_type, selected_goal, state, context)
                return selected_goal
            else:
                an_logger.warning(f"无法为意图类型 {intention_type} 找到合适的目标")
                return self._create_unknown_goal(f"无法处理意图类型 {intention_type}")
                
        except Exception as e:
            an_logger.error(f"目标选择失败: {e}")
            return self._create_unknown_goal(f"目标选择过程中发生错误: {str(e)}")
    
    def _validate_intention_state_compatibility(self, intention_type: str, current_state: WGStateEnum) -> bool:
        """验证意图类型与当前状态的兼容性"""
        # 特殊处理：清除工作流可以从任意状态触发
        if intention_type == self.INTENT_CLEAR_WORKFLOW:
            return current_state in self.CLEAR_WORKFLOW_ALLOWED_STATES
        
        # 其他意图类型需要检查状态兼容性
        allowed_intents = self.STATE_INTENT_MAPPING.get(current_state, [])
        is_compatible = intention_type in allowed_intents
        
        if not is_compatible:
            an_logger.warning(
                f"意图类型 {intention_type} 不兼容当前状态 {current_state.value}，"
                f"允许的意图类型: {allowed_intents}"
            )
        
        return is_compatible
    
    def _select_goal_by_intention(self, intention_type: str, goals: List[WGGoalType], 
                                 context: WGContext) -> Optional[WGGoalType]:
        """根据意图类型选择目标"""
        # 获取意图类型对应的Goal类型
        target_goal_type = self.INTENT_GOAL_MAPPING.get(intention_type)
        if not target_goal_type:
            an_logger.warning(f"未知的意图类型: {intention_type}")
            return None
        
        # 从目标列表中筛选出匹配的目标
        matching_goals = [goal for goal in goals if isinstance(goal, target_goal_type)]
        
        if not matching_goals:
            # 如果预定义目标列表中没有匹配的目标，创建一个默认目标
            return self._create_default_goal(intention_type, context)
        
        # 如果只有一个匹配的目标，直接返回
        if len(matching_goals) == 1:
            return matching_goals[0]
        
        # 如果有多个匹配的目标，选择优先级最高的
        return _select_highest_priority_goal(matching_goals)
    
    def _create_default_goal(self, intention_type: str, context: WGContext) -> WGGoalType:
        """为指定意图类型创建默认目标"""
        goal_type = self.INTENT_GOAL_MAPPING.get(intention_type, WGGoalUnknown)
        
        # 目标描述映射
        goal_descriptions = {
            self.INTENT_ACTIVE_WORKFLOW: "处理新的活动告警，启动完整的保障流程",
            self.INTENT_APPROVAL_PROCESS: "处理方案审批结果，推进审核流程",
            self.INTENT_EXECUTE_PROCESS: "处理方案执行结果，推进执行流程",
            self.INTENT_EVALUATE_PROCESS: "处理效果评估结果，推进评估流程",
            self.INTENT_CLEAR_WORKFLOW: "处理已清除告警，启动复位流程",
            self.INTENT_ROLLBACK_PROCESS: "处理方案回退结果，完成回退流程",
        }
        
        goal_names = {
            self.INTENT_ACTIVE_WORKFLOW: "新活动工作流",
            self.INTENT_APPROVAL_PROCESS: "审批流程",
            self.INTENT_EXECUTE_PROCESS: "执行流程",
            self.INTENT_EVALUATE_PROCESS: "评估流程",
            self.INTENT_CLEAR_WORKFLOW: "清除工作流",
            self.INTENT_ROLLBACK_PROCESS: "回退流程",
        }
        
        goal_id = f"wg_goal_{intention_type}_{context.session_id}"
        goal_name = goal_names.get(intention_type, f"意图{intention_type}")
        goal_description = goal_descriptions.get(intention_type, f"处理意图类型{intention_type}")
        priority = self.GOAL_PRIORITIES.get(goal_type, 50)
        
        # 创建目标实例
        return goal_type(
            id=goal_id,
            name=goal_name,
            description=goal_description,
            priority=priority,
        )

    def _create_unknown_goal(self, message: str) -> WGGoalUnknown:
        """创建未知目标"""
        return WGGoalUnknown(
            id="wg_goal_unknown",
            name="未知目标",
            description=message,
            priority=self.GOAL_PRIORITIES[WGGoalUnknown],
        )
    
    def _log_goal_selection(self, intention_type: str, selected_goal: WGGoalType, 
                           state: WGState, context: WGContext):
        """记录目标选择结果"""
        intention_descriptions = {
            self.INTENT_ACTIVE_WORKFLOW: "新活动工作流",
            self.INTENT_APPROVAL_PROCESS: "审批流程",
            self.INTENT_EXECUTE_PROCESS: "执行流程",
            self.INTENT_EVALUATE_PROCESS: "评估流程",
            self.INTENT_CLEAR_WORKFLOW: "清除工作流",
            self.INTENT_ROLLBACK_PROCESS: "回退流程",
        }
        
        intent_desc = intention_descriptions.get(intention_type, f"意图{intention_type}")
        serialno = context.serialno or "未知"
        
        an_logger.info(
            f"[{serialno}] 目标选择完成 - 意图: {intent_desc}, "
            f"当前状态: {state.state.value}, "
            f"选择目标: {selected_goal.name} (ID: {selected_goal.id})"
        )
        
        # 记录目标优先级信息
        if hasattr(selected_goal, 'priority'):
            an_logger.debug(f"[{serialno}] 目标优先级: {selected_goal.priority}")
    
    def get_supported_intentions(self) -> List[str]:
        """获取支持的意图类型列表"""
        return list(self.INTENT_GOAL_MAPPING.keys())
    
    def get_goal_type_for_intention(self, intention_type: str) -> Optional[Type[WGGoalType]]:
        """获取指定意图类型对应的Goal类型"""
        return self.INTENT_GOAL_MAPPING.get(intention_type)
    
    def is_intention_supported(self, intention_type: str) -> bool:
        """检查是否支持指定的意图类型"""
        return intention_type in self.INTENT_GOAL_MAPPING
