"""
无线保障智能体规划处理器

本模块实现了WGPlannerProcessor，负责根据状态和目标生成具体的执行计划。
为每种Goal类型创建对应的Plan实例，配置执行参数和上下文信息。

主要功能：
1. 根据Goal类型生成对应的Plan实例
2. 配置Plan的执行参数和上下文
3. 集成行为树代码配置
4. 添加计划生成的验证逻辑
5. 实现计划优化和调整机制

作者: RIPER-5 重构团队
日期: 2025-07-24
版本: 1.0.0
"""

from typing import Optional, ClassVar, Dict, Type

from an_copilot.framework.logging import an_logger
from an_copilot.framework.saida.core.interfaces import IPlannerProcessor

from src.agents.wireless_guarantee_agent_saida.wg_saida_model import (
    WGContext,
    WGState,
    WGGoalType,
    WGGoalActiveWorkflow,
    WGGoalApprovalProcess,
    WGGoalExecuteProcess,
    WGGoalEvaluateProcess,
    WGGoalClearWorkflow,
    WGGoalRollbackProcess,
    WGGoalUnknown,
    WGPlanType,
    WGPlanActiveWorkflow,
    WGPlanApprovalProcess,
    WGPlanExecuteProcess,
    WGPlanEvaluateProcess,
    WGPlanClearWorkflow,
    WGPlanRollbackProcess,
    WGPlanUnknown,
)


def _log_plan_creation(plan: WGPlanType, goal: WGGoalType,
                       state: WGState, context: WGContext):
    """记录计划创建结果"""
    serialno = context.serialno or "未知"
    intention_type = context.intention_type or "未知"

    an_logger.info(
        f"[{serialno}] 计划生成完成 - "
        f"目标: {goal.name}, "
        f"当前状态: {state.state.value}, "
        f"意图类型: {intention_type}, "
        f"生成计划: {plan.name} (类型: {type(plan).__name__})"
    )

    # 记录行为树代码信息
    if hasattr(plan, 'btree_code') and plan.btree_code:
        an_logger.info(f"[{serialno}] 计划行为树代码: {plan.btree_code}")

    # 记录计划优先级
    if hasattr(plan, 'priority'):
        an_logger.debug(f"[{serialno}] 计划优先级: {plan.priority}")


def _validate_plan(plan: WGPlanType, state: WGState, context: WGContext) -> bool:
    """验证计划的有效性"""
    try:
        # 基础验证
        if not plan.name or not plan.description:
            an_logger.warning("计划缺少名称或描述")
            return False

        if not plan.goal:
            an_logger.warning("计划缺少关联的目标")
            return False

        # 行为树代码验证（对于需要行为树的计划）
        if isinstance(plan, (WGPlanActiveWorkflow, WGPlanClearWorkflow)):
            if not hasattr(plan, 'btree_code') or not plan.btree_code:
                an_logger.warning(f"行为树计划缺少行为树代码: {plan.name}")
                return False

        # 上下文验证
        if not context.session_id or not context.request_id:
            an_logger.warning("计划缺少必要的会话信息")
            return False

        # 意图类型验证
        if not context.intention_type:
            an_logger.warning("计划缺少意图类型信息")
            return False

        return True

    except Exception as e:
        an_logger.error(f"计划验证过程中发生错误: {e}")
        return False


def _create_unknown_plan(goal: WGGoalType, context: WGContext, reason: str) -> WGPlanUnknown:
    """创建未知计划"""
    return WGPlanUnknown(
        name="未知计划",
        description=f"无法生成有效计划: {reason}",
        goal=goal,
        context=context,
    )


def _configure_plan_context(plan: WGPlanType, state: WGState, context: WGContext):
    """配置计划的执行上下文"""
    # 设置计划的状态信息
    if hasattr(plan, 'current_state'):
        plan.current_state = state.state

    # 设置计划的会话信息
    if hasattr(plan, 'session_id'):
        plan.session_id = context.session_id
    if hasattr(plan, 'request_id'):
        plan.request_id = context.request_id

    # 设置计划的业务信息
    if hasattr(plan, 'serialno'):
        plan.serialno = context.serialno
    if hasattr(plan, 'scheme_id'):
        plan.scheme_id = context.scheme_id
    if hasattr(plan, 'intention_type'):
        plan.intention_type = context.intention_type

    # 设置计划的告警信息
    if hasattr(plan, 'alarm_info') and context.alarm_info:
        plan.alarm_info = context.alarm_info

    # 设置计划的状态机上下文
    if hasattr(plan, 'sm_context') and context.sm_context:
        plan.sm_context = context.sm_context


class WGPlannerProcessor(
    IPlannerProcessor[WGState, WGGoalType, WGPlanType, WGContext]
):
    """
    无线保障智能体规划处理器
    
    根据状态和目标生成具体的执行计划，为每种Goal类型创建对应的Plan实例。
    配置Plan的执行参数和上下文，集成行为树代码配置，确保计划的完整性和可执行性。
    """
    
    # 意图类型常量（与原系统保持一致）
    INTENT_ACTIVE_WORKFLOW: ClassVar[str] = "1"
    INTENT_APPROVAL_PROCESS: ClassVar[str] = "2"
    INTENT_EXECUTE_PROCESS: ClassVar[str] = "3"
    INTENT_EVALUATE_PROCESS: ClassVar[str] = "4"
    INTENT_CLEAR_WORKFLOW: ClassVar[str] = "5"
    INTENT_ROLLBACK_PROCESS: ClassVar[str] = "6"
    
    # Goal类型到Plan类型的映射关系
    GOAL_PLAN_MAPPING: ClassVar[Dict[Type[WGGoalType], Type[WGPlanType]]] = {
        WGGoalActiveWorkflow: WGPlanActiveWorkflow,
        WGGoalApprovalProcess: WGPlanApprovalProcess,
        WGGoalExecuteProcess: WGPlanExecuteProcess,
        WGGoalEvaluateProcess: WGPlanEvaluateProcess,
        WGGoalClearWorkflow: WGPlanClearWorkflow,
        WGGoalRollbackProcess: WGPlanRollbackProcess,
        WGGoalUnknown: WGPlanUnknown,
    }
    
    # 意图类型到行为树代码的映射（与原系统配置一致）
    INTENTION_BTREE_MAPPING: ClassVar[Dict[str, str]] = {
        INTENT_ACTIVE_WORKFLOW: "wireless-guarantee-scheme-generate",  # 新活动工作流
        INTENT_CLEAR_WORKFLOW: "wireless-guarantee-reset",            # 清除工作流
    }
    
    # 计划优先级配置
    PLAN_PRIORITIES: ClassVar[Dict[Type[WGPlanType], int]] = {
        WGPlanActiveWorkflow: 100,      # 新活动工作流优先级最高
        WGPlanClearWorkflow: 90,        # 清除工作流优先级次高
        WGPlanApprovalProcess: 80,      # 审批流程
        WGPlanExecuteProcess: 70,       # 执行流程
        WGPlanEvaluateProcess: 60,      # 评估流程
        WGPlanRollbackProcess: 50,      # 回退流程
        WGPlanUnknown: 10,              # 未知计划优先级最低
    }
    
    def __init__(self):
        """初始化规划处理器"""
        # 延迟初始化避免序列化问题
        self._settings = None
        self._btree_codes = None

        an_logger.info("WGPlannerProcessor 初始化完成")

    @property
    def settings(self):
        """延迟初始化settings"""
        if self._settings is None:
            from src.config import settings
            self._settings = settings
        return self._settings

    @property
    def btree_codes(self):
        """延迟初始化btree_codes"""
        if self._btree_codes is None:
            self._btree_codes = getattr(self.settings.config.wg_agent_config, 'btree_codes', {})
        return self._btree_codes
    
    def process(
        self, state: WGState, goal: WGGoalType, context: WGContext
    ) -> WGPlanType:
        """
        根据状态和目标生成具体的执行计划
        
        Args:
            state: 当前状态对象
            goal: 目标对象
            context: 上下文对象
            
        Returns:
            WGPlanType: 生成的计划对象
            
        Raises:
            ValueError: 当目标类型不支持时抛出
        """
        try:
            # 获取目标对应的Plan类型
            plan_class = self.GOAL_PLAN_MAPPING.get(type(goal))
            if not plan_class:
                an_logger.warning(f"不支持的目标类型: {type(goal)}")
                return _create_unknown_plan(goal, context, "不支持的目标类型")
            
            # 生成计划实例
            plan = self._create_plan_instance(plan_class, goal, state, context)
            
            # 验证计划有效性
            if _validate_plan(plan, state, context):
                _log_plan_creation(plan, goal, state, context)
                return plan
            else:
                an_logger.warning(f"计划验证失败: {plan.name}")
                return _create_unknown_plan(goal, context, "计划验证失败")
                
        except Exception as e:
            an_logger.error(f"计划生成失败: {e}")
            return _create_unknown_plan(goal, context, f"计划生成过程中发生错误: {str(e)}")
    
    def _create_plan_instance(self, plan_class: Type[WGPlanType], goal: WGGoalType, 
                             state: WGState, context: WGContext) -> WGPlanType:
        """创建计划实例"""
        # 生成计划名称和描述
        plan_name = f"计划_{goal.name}"
        plan_description = f"执行{goal.description}"
        
        # 获取计划优先级
        priority = self.PLAN_PRIORITIES.get(plan_class, 50)
        
        # 基础计划参数
        plan_params = {
            "name": plan_name,
            "description": plan_description,
            "goal": goal,
            "context": context,
            "priority": priority,
        }
        
        # 为需要行为树的计划添加行为树代码
        if plan_class in [WGPlanActiveWorkflow, WGPlanClearWorkflow]:
            btree_code = self._get_btree_code_for_goal(goal, context)
            if btree_code:
                plan_params["btree_code"] = btree_code
        
        # 创建计划实例
        plan = plan_class(**plan_params)
        
        # 设置计划的执行上下文
        _configure_plan_context(plan, state, context)
        
        return plan
    
    def _get_btree_code_for_goal(self, goal: WGGoalType, context: WGContext) -> Optional[str]:
        """获取目标对应的行为树代码"""
        intention_type = context.intention_type
        if not intention_type:
            return None
        
        # 从意图类型映射获取行为树代码
        btree_code = self.INTENTION_BTREE_MAPPING.get(intention_type)
        if btree_code:
            an_logger.info(f"为意图类型 {intention_type} 分配行为树代码: {btree_code}")
            return btree_code
        
        # 从配置文件获取行为树代码（备用方案）
        if isinstance(goal, WGGoalActiveWorkflow):
            return self.btree_codes.get("start_generate_scheme_process", "wireless-guarantee-scheme-generate")
        elif isinstance(goal, WGGoalClearWorkflow):
            return self.btree_codes.get("start_reset_process", "wireless-guarantee-reset")
        
        return None

    def get_supported_goal_types(self) -> list:
        """获取支持的目标类型列表"""
        return list(self.GOAL_PLAN_MAPPING.keys())
    
    def get_plan_type_for_goal(self, goal_type: Type[WGGoalType]) -> Optional[Type[WGPlanType]]:
        """获取指定目标类型对应的计划类型"""
        return self.GOAL_PLAN_MAPPING.get(goal_type)
    
    def is_goal_type_supported(self, goal_type: Type[WGGoalType]) -> bool:
        """检查是否支持指定的目标类型"""
        return goal_type in self.GOAL_PLAN_MAPPING
